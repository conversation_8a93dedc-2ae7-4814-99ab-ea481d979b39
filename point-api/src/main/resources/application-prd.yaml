cloud:
  aws:
    credentials:
      instance-profile: false
      profile-name:
common:
  log:
    console:
      appender: CONSOLE_JSON
management:
  endpoints:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  metrics:
    export:
      cloudwatch:
        enabled: true
      prometheus:
        enabled: true
spring:
  config:
    domain: backseat-service.com
    environment: prd
  datasource:
    master:
      maximum-pool-size: 150
    historical:
      driver-class-name: com.amazon.redshift.jdbc42.Driver
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false

coin:
  cus:
    host: https://backseat-service.com
    host-external: https://backseat-service.com
