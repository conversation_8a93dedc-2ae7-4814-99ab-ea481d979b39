package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Timed
@RestController
@RequestMapping("/admin/v1/quiz")
@RequiredArgsConstructor
public class V1QuizManagerRestController extends ExchangeAdminController {

    @RequestMapping("/list")
    @PreAuthorize("@auth.check('quiz-list')")
    public ResponseEntity<?> page() {
        return ResponseEntity.ok().build();
    }
}
