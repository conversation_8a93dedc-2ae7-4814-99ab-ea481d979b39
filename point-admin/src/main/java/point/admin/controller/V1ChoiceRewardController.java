package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import point.common.model.request.ChoiceRewardSearchForm;
import point.common.model.response.ChoiceRewardPageData;
import point.common.model.response.PageData;
import point.common.service.ChoiceRewardService;

@Timed
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/choice/reward")
public class V1ChoiceRewardController extends ExchangeAdminController {

    private final ChoiceRewardService choiceRewardService;

    @GetMapping
    @PreAuthorize("@auth.check('user-rewards-list')")
    public ResponseEntity<PageData<ChoiceRewardPageData>> get(ChoiceRewardSearchForm searchForm) {
        return ResponseEntity.ok(choiceRewardService.findRewardByCondition(searchForm));
    }
}
