package point.admin.controller;

import io.micrometer.core.annotation.Timed;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import point.common.constant.*;
import point.common.entity.*;
import point.common.model.request.MonsterBaseBoRequest;
import point.common.model.response.MonsterBoResponse;
import point.common.model.response.MonsterCropsEatenResponse;
import point.common.model.response.MonsterGrowthHistoryResponse;
import point.common.model.response.PageData;
import point.common.service.*;
import point.common.util.DateUnit;
import point.pos.service.PosTradeService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/monster")
@Timed
public class MonsterInfoBoController {

    private final UserMonsterInfoService userMonsterInfoService;
    private final MonsterBaseService monsterBaseService;
    private final PosTradeService posTradeService;
    private final MonsterGrowthHistoryService monsterGrowthHistoryService;

    @GetMapping("/monster-list")
    @PreAuthorize("@auth.check('monster-list')")
    public ResponseEntity<PageData<MonsterBoResponse>> get(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "idType", required = false) UserIdType idType,
            @RequestParam(value = "monsterName", required = false) String monsterName,
            @RequestParam(value = "level", required = false) Integer level,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        PageData<UserMonsterInfo> userMonsterInfoPageData =
                userMonsterInfoService.findByCondition(
                        userId, idType, monsterName, level, number, size);
        List<UserMonsterInfo> userMonsterInfoList = userMonsterInfoPageData.getContent();

        List<MonsterBoResponse> summaryList = new ArrayList<>();
        for (UserMonsterInfo userMonsterInfo : userMonsterInfoList) {
            MonsterBoResponse monsterBoResponse = new MonsterBoResponse();
            monsterBoResponse.setUserId(userMonsterInfo.getUserId());
            monsterBoResponse.setIdType(userMonsterInfo.getIdType());
            monsterBoResponse.setMonsterName(userMonsterInfo.getMonsterBase().getName());
            monsterBoResponse.setMonsterLevel(userMonsterInfo.getLevel());
            monsterBoResponse.setCurrentExperience(userMonsterInfo.getCurrentExperience());
            monsterBoResponse.setNextExperience(userMonsterInfo.getNextLevelExperience());
            monsterBoResponse.setCreationDateTime(userMonsterInfo.getCreationDateTime());
            monsterBoResponse.setMonthlyPower(userMonsterInfo.getMonthlyPower());
            summaryList.add(monsterBoResponse);
        }

        // creationDateTime の降順で並べ替え
        summaryList.sort(Comparator.comparing(MonsterBoResponse::getCreationDateTime).reversed());

        PageData<MonsterBoResponse> pageData =
                new PageData<MonsterBoResponse>(
                        userMonsterInfoPageData.getNumber(),
                        userMonsterInfoPageData.getSize(),
                        userMonsterInfoPageData.getTotalElements(),
                        summaryList);

        return ResponseEntity.ok(pageData);
    }

    @GetMapping("/monster-base")
    public ResponseEntity<PageData<MonsterBase>> getMonsterBase(
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        PageData<MonsterBase> monsterBasePageData =
                monsterBaseService.findByCondition(number, size);

        return ResponseEntity.ok(monsterBasePageData);
    }

    @PutMapping("/update-monster-base")
    public ResponseEntity<MonsterBase> updateMonsterBase(@RequestBody MonsterBaseBoRequest request)
            throws Exception {
        MonsterBase monsterBase = monsterBaseService.findOne(request.getId());
        monsterBase.setName(request.getName());
        monsterBase.setImgUrl(request.getImgUrl());
        return ResponseEntity.ok().body(monsterBaseService.save(monsterBase));
    }

    @GetMapping("/crops-eaten")
    public ResponseEntity<PageData<MonsterCropsEatenResponse>> getCropsEaten(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        PageData<MonsterCropsEatenResponse> monsterBasePageData =
                posTradeService.getCropsEatenByCondition(userId, number, size);

        return ResponseEntity.ok(monsterBasePageData);
    }

    @GetMapping("/growth-history")
    public ResponseEntity<PageData<MonsterGrowthHistoryResponse>> getGrowthHistory(
            @RequestParam(value = "userId", required = true) Long userId,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "symbolId", required = false) Long symbolId,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {

        Date dateFromDate = dateFrom != null ? new Date(dateFrom) : null;
        Date dateToDate = dateTo != null ? DateUnit.getTommorowStartDate(new Date(dateTo)) : null;

        PageData<MonsterGrowthHistory> monsterFoodHistoryPageData =
                monsterGrowthHistoryService.getTradeSummaryByCondition(
                        userId, symbolId, dateFromDate, dateToDate, number, size);
        List<MonsterGrowthHistory> monsterFoodHistoryList = monsterFoodHistoryPageData.getContent();

        List<MonsterGrowthHistoryResponse> summaryList = new ArrayList<>();
        for (MonsterGrowthHistory monsterGrowthHistory : monsterFoodHistoryList) {
            MonsterGrowthHistoryResponse response = new MonsterGrowthHistoryResponse();
            response.setId(monsterGrowthHistory.getId());
            response.setUserId(monsterGrowthHistory.getUserId());
            response.setSymbol(
                    CurrencyTypeMonster.getMonsterCurrencyTypeById(
                            monsterGrowthHistory.getSymbolId()));
            response.setCrop(
                    CurrencyTypeMonster.getMonsterFruitNameById(
                            monsterGrowthHistory.getSymbolId()));
            response.setIncome(monsterGrowthHistory.getIncome().setScale(0, RoundingMode.DOWN));
            BigDecimal conversionRate = monsterGrowthHistory.getConversionRate();
            if (conversionRate != null && conversionRate.compareTo(BigDecimal.ZERO) > 0) {
                response.setConversionRate(
                        conversionRate
                                        .multiply(new BigDecimal("100"))
                                        .setScale(0, BigDecimal.ROUND_HALF_UP)
                                        .toPlainString()
                                + "%");
            } else {
                response.setConversionRate("-");
            }
            response.setWeekFood(
                    CurrencyTypeMonster.getMonsterFruitNameById(
                            monsterGrowthHistory.getWeekFood()));
            BigDecimal bonusRate = monsterGrowthHistory.getBonusRate();
            if (bonusRate != null && bonusRate.compareTo(BigDecimal.ZERO) > 0) {
                response.setBonusRate("10%");
            } else {
                response.setBonusRate("-");
            }
            response.setExperienceEarned(monsterGrowthHistory.getExperienceEarned());
            response.setCurrentExperience(monsterGrowthHistory.getCurrentExperience());
            response.setNextLevelExperience(monsterGrowthHistory.getNextLevelExperience());
            BigDecimal growthRate = monsterGrowthHistory.getGrowthRate();
            if (growthRate != null && growthRate.compareTo(BigDecimal.ZERO) > 0) {
                response.setGrowthRate(
                        growthRate
                                        .multiply(new BigDecimal("100"))
                                        .setScale(0, BigDecimal.ROUND_HALF_UP)
                                        .toPlainString()
                                + "%");
            } else {
                response.setGrowthRate("-");
            }
            response.setLevelBefore(monsterGrowthHistory.getLevelBefore());
            response.setLevelAfter(monsterGrowthHistory.getLevelAfter());
            response.setLastUpdateTime(monsterGrowthHistory.getCreatedAt());
            summaryList.add(response);
        }

        summaryList.sort(
                Comparator.comparing(MonsterGrowthHistoryResponse::getLastUpdateTime).reversed());

        PageData<MonsterGrowthHistoryResponse> pageData =
                new PageData<MonsterGrowthHistoryResponse>(
                        monsterFoodHistoryPageData.getNumber(),
                        monsterFoodHistoryPageData.getSize(),
                        monsterFoodHistoryPageData.getTotalElements(),
                        summaryList);
        return ResponseEntity.ok(pageData);
    }
}
