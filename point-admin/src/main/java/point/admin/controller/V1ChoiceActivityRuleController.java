package point.admin.controller;

import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.admin.entity.AdminUser;
import point.common.constant.*;
import point.common.entity.ChoiceActivityRule;
import point.common.exception.CustomException;
import point.common.model.request.ChoiceActivityRuleForm;
import point.common.model.response.PageData;
import point.common.service.ChoiceActivityRuleService;

@RestController
@RequestMapping("/admin/v1/choice-activity-rule")
@RequiredArgsConstructor
public class V1ChoiceActivityRuleController extends ExchangeAdminController {

    public final ChoiceActivityRuleService choiceActivityRuleService;

    @GetMapping()
    @PreAuthorize("@auth.check('choice-activity-rule-list')")
    public ResponseEntity<PageData<ChoiceActivityRule>> get(
            @RequestParam(value = "activityName", required = false) String activityName,
            @RequestParam(value = "status", required = false) ChoiceActivityStatus status,
            @RequestParam(value = "obtainFrequency", required = false)
                    ChoiceObtainFrequency obtainFrequency,
            @RequestParam(value = "powerAmountFrom", required = false) Long powerAmountFrom,
            @RequestParam(value = "powerAmountTo", required = false) Long powerAmountTo,
            @RequestParam(value = "startTimeFrom", required = false) Long startTimeFrom,
            @RequestParam(value = "startTimeTo", required = false) Long startTimeTo,
            @RequestParam(value = "endTimeFrom", required = false) Long endTimeFrom,
            @RequestParam(value = "endTimeTo", required = false) Long endTimeTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        PageData<ChoiceActivityRule> pageData =
                choiceActivityRuleService.findByCondition(
                        activityName,
                        status,
                        obtainFrequency,
                        powerAmountFrom,
                        powerAmountTo,
                        startTimeFrom,
                        startTimeTo,
                        endTimeFrom,
                        endTimeTo,
                        number,
                        size);
        return ResponseEntity.ok(pageData);
    }

    @PostMapping()
    @PreAuthorize("@auth.check('choice-activity-rule-list')")
    public ResponseEntity<ChoiceActivityRule> save(
            @AuthenticationPrincipal AdminUser adminuser, @RequestBody ChoiceActivityRuleForm form)
            throws Exception {
        form.setCreatedBy(adminuser.getEmail());
        return ResponseEntity.ok().body(choiceActivityRuleService.create(form));
    }

    @PutMapping
    @PreAuthorize("@auth.check('choice-activity-rule-list')")
    public ResponseEntity<ChoiceActivityRule> update(
            @AuthenticationPrincipal AdminUser adminuser, @RequestBody ChoiceActivityRuleForm form)
            throws Exception {
        if (form.getId() == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PARAMETER_ERROR);
        }
        ChoiceActivityRule choiceActivityRule = choiceActivityRuleService.findOne(form.getId());
        choiceActivityRule.setActivityName(form.getActivityName());
        choiceActivityRule.setActivityFunction(form.getActivityFunction());
        choiceActivityRule.setGetType(form.getGetType());
        choiceActivityRule.setPowerAmountRate(form.getPowerAmountRate());
        choiceActivityRule.setObtainFrequency(form.getObtainFrequency());
        if (!StringUtils.isEmpty(form.getPowerAmount())) {
            choiceActivityRule.setPowerAmount(Long.parseLong(form.getPowerAmount()));
        }
        choiceActivityRule.setUpdatedBy(adminuser.getEmail());
        choiceActivityRuleService.save(choiceActivityRule);
        return ResponseEntity.ok()
                .body(choiceActivityRuleService.findOne(choiceActivityRule.getId()));
    }

    @DeleteMapping("/delete")
    @PreAuthorize("@auth.check('choice-activity-rule-list')")
    public ResponseEntity<ChoiceActivityRule> delete(
            @AuthenticationPrincipal AdminUser adminuser, @RequestParam(value = "id") Long id)
            throws Exception {
        if (id == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PARAMETER_ERROR);
        }
        ChoiceActivityRule choiceActivityRule = new ChoiceActivityRule();
        choiceActivityRule.setId(id);
        choiceActivityRuleService.delete(choiceActivityRule);
        return ResponseEntity.ok()
                .body(choiceActivityRuleService.findOne(choiceActivityRule.getId()));
    }

    @GetMapping("/detail")
    @PreAuthorize("@auth.check('choice-activity-rule-list')")
    public ResponseEntity<ChoiceActivityRule> get(@RequestParam(value = "id") Long id)
            throws Exception {
        if (id == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PARAMETER_ERROR);
        }
        ChoiceActivityRule choiceActivityRule = choiceActivityRuleService.findOne(id);
        return ResponseEntity.ok().body(choiceActivityRule);
    }

    @GetMapping("/select")
    @PreAuthorize("@auth.check('choice-activity-rule-list','choice-power-transfer-list')")
    public ResponseEntity<List<ChoiceActivityRule>> get() throws Exception {
        List<ChoiceActivityRule> choiceActivityRuleList = new ArrayList<>();
        choiceActivityRuleService.findAll().stream()
                .map(
                        choiceActivityRuleInfo -> {
                            ChoiceActivityRule choiceActivityRule = new ChoiceActivityRule();
                            choiceActivityRule.setId(choiceActivityRuleInfo.getId());
                            choiceActivityRule.setActivityName(
                                    choiceActivityRuleInfo.getActivityName());
                            return choiceActivityRule;
                        })
                .forEach(choiceActivityRuleList::add);

        return ResponseEntity.ok(choiceActivityRuleList);
    }

    @GetMapping("/check-active")
    @PreAuthorize("@auth.check('choice-activity-rule-list')")
    public ResponseEntity<Boolean> checkActive(
            @RequestParam(value = "activityName", required = false) String activityName,
            @RequestParam(value = "activityFunction", required = false) String activityFunction,
            @RequestParam(value = "status", required = false) String status)
            throws Exception {

        // 将字符串参数转换为枚举类型
        ChoiceActivityFunction functionEnum = null;
        if (activityFunction != null) {
            try {
                functionEnum = ChoiceActivityFunction.valueOf(activityFunction);
            } catch (IllegalArgumentException e) {
            }
        }

        ChoiceActivityStatus statusEnum = null;
        if (status != null) {
            try {
                statusEnum = ChoiceActivityStatus.valueOf(status);
            } catch (IllegalArgumentException e) {
            }
        }

        boolean exists =
                choiceActivityRuleService.existsActiveRecord(
                        activityName, functionEnum, statusEnum);
        return ResponseEntity.ok(exists);
    }
}
