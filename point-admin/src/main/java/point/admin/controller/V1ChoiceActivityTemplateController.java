package point.admin.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import point.admin.entity.AdminUser;
import point.common.constant.ChoiceActivityAutoCycle;
import point.common.constant.ChoiceActivityStatus;
import point.common.constant.ErrorCode;
import point.common.constant.ViewVariables;
import point.common.entity.ChoiceActivityTemplate;
import point.common.exception.CustomException;
import point.common.model.request.ChoiceActivityTemplateForm;
import point.common.model.response.PageData;
import point.common.service.ChoiceActivityTemplateService;

@RestController
@RequestMapping("/admin/v1/choice-activity-template")
@RequiredArgsConstructor
public class V1ChoiceActivityTemplateController extends ExchangeAdminController {

    public final ChoiceActivityTemplateService choiceActivityTemplateService;

    @GetMapping()
    @PreAuthorize("@auth.check('choice-activity-template-list')")
    public ResponseEntity<PageData<ChoiceActivityTemplate>> get(
            @RequestParam(value = "status", required = false) ChoiceActivityStatus status,
            @RequestParam(value = "startTimeFrom", required = false) Long startTimeFrom,
            @RequestParam(value = "startTimeTo", required = false) Long startTimeTo,
            @RequestParam(value = "endTimeFrom", required = false) Long endTimeFrom,
            @RequestParam(value = "endTimeTo", required = false) Long endTimeTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        PageData<ChoiceActivityTemplate> pageData =
                choiceActivityTemplateService.findByCondition(
                        status, startTimeFrom, startTimeTo, endTimeFrom, endTimeTo, number, size);
        return ResponseEntity.ok(pageData);
    }

    @PostMapping()
    @PreAuthorize("@auth.check('choice-activity-template-list')")
    public ResponseEntity<ChoiceActivityTemplate> save(
            @AuthenticationPrincipal AdminUser adminuser,
            @RequestBody ChoiceActivityTemplateForm form)
            throws Exception {
        form.setCreatedBy(adminuser.getEmail());
        form.setStatus(ChoiceActivityStatus.ACTIVE);
        return ResponseEntity.ok().body(choiceActivityTemplateService.create(form));
    }

    @PutMapping
    @PreAuthorize("@auth.check('choice-activity-template-list')")
    public ResponseEntity<ChoiceActivityTemplate> update(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestBody ChoiceActivityTemplateForm form)
            throws Exception {

        if (form.getId() == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PARAMETER_ERROR);
        }
        ChoiceActivityTemplate choiceActivityTemplate =
                choiceActivityTemplateService.findOne(form.getId());
        choiceActivityTemplate.setActivityType(form.getActivityType());
        choiceActivityTemplate.setVoteStartTime(form.getVoteStartTime());
        choiceActivityTemplate.setVoteEndTime(form.getVoteEndTime());
        choiceActivityTemplate.setStartTime(form.getStartTime());
        choiceActivityTemplate.setEndTime(form.getEndTime());
        choiceActivityTemplate.setVotePowerLimit(form.getVotePowerLimit());
        choiceActivityTemplate.setStatus(form.getStatus());
        choiceActivityTemplate.setAutoCycle(form.getAutoCycle());
        choiceActivityTemplate.setPowerTotal(form.getPowerTotal());
        choiceActivityTemplate.setUpdatedBy(adminUser.getEmail());
        ChoiceActivityTemplate updatedTemplate =
                choiceActivityTemplateService.save(choiceActivityTemplate);

        return ResponseEntity.ok().body(updatedTemplate);
    }

    @DeleteMapping("/delete")
    @PreAuthorize("@auth.check('choice-activity-template-list')")
    public ResponseEntity<ChoiceActivityTemplate> delete(
            @AuthenticationPrincipal AdminUser adminuser, @RequestParam(value = "id") Long id)
            throws Exception {
        if (id == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PARAMETER_ERROR);
        }
        ChoiceActivityTemplate choiceActivityTemplate = choiceActivityTemplateService.findOne(id);
        choiceActivityTemplate.setStatus(ChoiceActivityStatus.ABOLISH);
        choiceActivityTemplate.setUpdatedBy(adminuser.getEmail());
        choiceActivityTemplateService.save(choiceActivityTemplate);
        return ResponseEntity.ok()
                .body(choiceActivityTemplateService.findOne(choiceActivityTemplate.getId()));
    }

    @GetMapping("/detail")
    @PreAuthorize("@auth.check('choice-activity-template-list')")
    public ResponseEntity<ChoiceActivityTemplate> get(@RequestParam(value = "id") Long id)
            throws Exception {
        if (id == null) {
            throw new CustomException(ErrorCode.REQUEST_ERROR_PARAMETER_ERROR);
        }
        ChoiceActivityTemplate choiceActivityTemplate = choiceActivityTemplateService.findOne(id);
        return ResponseEntity.ok().body(choiceActivityTemplate);
    }

    @GetMapping("/check-auto-cycle")
    @PreAuthorize("@auth.check('choice-activity-template-list')")
    public ResponseEntity<Boolean> checkAutoCycle(
            @RequestParam(value = "autoCycle") ChoiceActivityAutoCycle autoCycle) throws Exception {

        boolean exists =
                choiceActivityTemplateService.existsByAutoCycleAndStatus(
                        autoCycle, ChoiceActivityStatus.ACTIVE);
        return ResponseEntity.ok(exists);
    }
}
