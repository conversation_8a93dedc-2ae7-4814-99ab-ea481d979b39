package point.admin.controller;

import io.micrometer.core.instrument.util.StringUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.entity.AdminUser;
import point.admin.model.request.UserPutForm;
import point.admin.model.response.ReportUserDetail;
import point.admin.model.response.ReportUserNormal;
import point.admin.model.response.UserDetailWrapper;
import point.common.component.CsvDownloadManager;
import point.common.constant.CommonConstants;
import point.common.constant.ErrorCode;
import point.common.constant.KycStatus;
import point.common.constant.UserNoteTypeEnum;
import point.common.constant.UserStatus;
import point.common.constant.ViewVariables;
import point.common.entity.AffiliateInfo;
import point.common.entity.User;
import point.common.entity.UserInfo;
import point.common.entity.UserLoginInfo;
import point.common.exception.CustomException;
import point.common.model.dto.UserNoteDTO;
import point.common.model.request.UserNoteCreateForm;
import point.common.model.response.PageData;
import point.common.model.response.UserLoginInfoReportData;
import point.common.service.AffiliateInfoService;
import point.common.service.UserAgreementService;
import point.common.service.UserInfoService;
import point.common.service.UserLoginInfoService;
import point.common.service.UserNoteService;
import point.common.service.UserService;
import point.common.util.BindingResultUtil;
import point.common.util.DateUnit;

@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/user")
public class V1UserRestController extends ExchangeAdminController {

    private final UserService userService;
    private final UserInfoService userInfoService;
    private final CsvDownloadManager<ReportUserNormal> normalDownloadManager;
    private final CsvDownloadManager<ReportUserDetail> detailDownloadManager;
    private final UserNoteService userNoteService;
    private final UserAgreementService userAgreementService;
    private static final String REPORT_NORMAL_PREFIX = "report_normal";
    private final AffiliateInfoService affiliateInfoService;
    private final UserLoginInfoService userLoginInfoService;

    @Autowired private final CsvDownloadManager<UserLoginInfoReportData> downloadManager;
    private final String reportPreFix = "ログイン履歴照会";

    @GetMapping
    @PreAuthorize("@auth.check('assets')")
    public ResponseEntity<PageData<UserDetailWrapper>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "email", required = false) String email,
            @RequestParam(value = "firstName", required = false) String firstName,
            @RequestParam(value = "lastName", required = false) String lastName,
            @RequestParam(value = "firstKana", required = false) String firstKana,
            @RequestParam(value = "lastKana", required = false) String lastKana,
            @RequestParam(value = "userStatus", required = false) String userStatus,
            @RequestParam(value = "affiliateInfoId", required = false) Long affiliateInfoId,
            @RequestParam(value = "uuid", required = false) String uuid,
            @RequestParam(value = "insideAccountFlg", required = false) Boolean insideAccountFlg,
            @RequestParam(value = "kycStatus", required = false) String kycStatus,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        PageData<User> userPage =
                userService.findByCondition(
                        id,
                        idFrom,
                        idTo,
                        email,
                        firstName,
                        lastName,
                        firstKana,
                        lastKana,
                        UserStatus.valueOfName(userStatus),
                        affiliateInfoId,
                        uuid,
                        insideAccountFlg,
                        KycStatus.valueOfName(kycStatus),
                        dateFrom,
                        dateTo,
                        number,
                        size);

        List<User> content = userPage.getContent();
        if (!CollectionUtils.isEmpty(content)) {
            List<UserDetailWrapper> detailWrapperList =
                    content.stream()
                            .map(
                                    it -> {
                                        UserDetailWrapper userDetailWrapper =
                                                new UserDetailWrapper();
                                        userDetailWrapper.setUser(it);
                                        if (id != null) {
                                            userDetailWrapper.setUserAgreements(
                                                    userAgreementService
                                                            .findEnabledAgreementByUserId(id));
                                            userDetailWrapper.setNote(
                                                    userNoteService
                                                            .findByUserId(id)
                                                            .map(UserNoteDTO::getNote)
                                                            .orElse(CommonConstants.EMPTY));
                                        }
                                        return userDetailWrapper;
                                    })
                            .toList();
            return ResponseEntity.ok(
                    new PageData<>(number, size, userPage.getTotalElements(), detailWrapperList));
        }
        return ResponseEntity.ok(new PageData<>(number, size, 0, Collections.emptyList()));
    }

    @GetMapping("/download/normal")
    public void downloadNormal(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "email", required = false) String email,
            @RequestParam(value = "firstName", required = false) String firstName,
            @RequestParam(value = "lastName", required = false) String lastName,
            @RequestParam(value = "firstKana", required = false) String firstKana,
            @RequestParam(value = "lastKana", required = false) String lastKana,
            @RequestParam(value = "userStatus", required = false) String userStatus,
            @RequestParam(value = "affiliateInfoId", required = false) Long affiliateInfoId,
            @RequestParam(value = "uuid", required = false) String uuid,
            @RequestParam(value = "insideAccountFlg", required = false) Boolean insideAccountFlg,
            @RequestParam(value = "kycStatus", required = false) String kycStatus,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        // レポート作成
        List<User> userList =
                userService
                        .findByCondition(
                                id,
                                idFrom,
                                idTo,
                                email,
                                firstName,
                                lastName,
                                firstKana,
                                lastKana,
                                UserStatus.valueOfName(userStatus),
                                affiliateInfoId,
                                uuid,
                                insideAccountFlg,
                                KycStatus.valueOfName(kycStatus),
                                dateFrom,
                                dateTo,
                                0,
                                Integer.MAX_VALUE)
                        .getContent();
        List<ReportUserNormal> reportDataList = new ArrayList<>();

        // 内部でExceptionを投げるため、streamでなく拡張for文を使用する
        for (User user : userList) {
            reportDataList.add(new ReportUserNormal(user));
        }
        normalDownloadManager.download(response, reportDataList, REPORT_NORMAL_PREFIX, true);
    }

    @GetMapping("/download/detail")
    public void downloadDetail(
            @AuthenticationPrincipal AdminUser adminUser,
            HttpServletResponse response,
            @RequestParam(value = "id", required = false) Long id,
            @RequestParam(value = "idFrom", required = false) Long idFrom,
            @RequestParam(value = "idTo", required = false) Long idTo,
            @RequestParam(value = "email", required = false) String email,
            @RequestParam(value = "firstName", required = false) String firstName,
            @RequestParam(value = "lastName", required = false) String lastName,
            @RequestParam(value = "firstKana", required = false) String firstKana,
            @RequestParam(value = "lastKana", required = false) String lastKana,
            @RequestParam(value = "userStatus", required = false) String userStatus,
            @RequestParam(value = "affiliateInfoId", required = false) Long affiliateInfoId,
            @RequestParam(value = "uuid", required = false) String uuid,
            @RequestParam(value = "insideAccountFlg", required = false) Boolean insideAccountFlg,
            @RequestParam(value = "kycStatus", required = false) String kycStatus,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        // レポート作成
        List<User> userList =
                userService
                        .findByCondition(
                                id,
                                idFrom,
                                idTo,
                                email,
                                firstName,
                                lastName,
                                firstKana,
                                lastKana,
                                UserStatus.valueOfName(userStatus),
                                affiliateInfoId,
                                uuid,
                                insideAccountFlg,
                                KycStatus.valueOfName(kycStatus),
                                dateFrom,
                                dateTo,
                                0,
                                Integer.MAX_VALUE)
                        .getContent();
        List<ReportUserDetail> reportDataList = new ArrayList<>();

        // 内部でExceptionを投げるため、streamでなく拡張for文を使用する
        for (User user : userList) {
            reportDataList.add(new ReportUserDetail(user));
        }
        detailDownloadManager.download(response, reportDataList, REPORT_NORMAL_PREFIX, true);
    }

    @PutMapping
    public ResponseEntity<Object> put(
            @AuthenticationPrincipal AdminUser adminUser,
            @Valid @RequestBody UserPutForm form,
            BindingResult result)
            throws Exception {

        User user = userService.findOne(form.getId());
        if (user == null) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_USER);
        }

        // validation
        BindingResultUtil bindingResultUtil = new BindingResultUtil();
        if (bindingResultUtil.hasErrors(result)) {
            return ResponseEntity.badRequest().body(bindingResultUtil.getErrorList());
        }

        UserInfo userInfo = userInfoService.findOne(user.getUserInfoId());
        if (userInfo == null) {
            userInfo = new UserInfo(form.getId());
        }
        form.getUserInfo().setInsider(form.getInsider());
        userInfo.setInsider(form.getInsider());
        userInfo = userInfoService.save(userInfo.setProperties(form.getUserInfo()));

        user.setEmail(form.getEmail());
        user.setUserInfoId(userInfo.getId());
        user.setUserStatus(UserStatus.valueOf(form.getUserStatus()));
        user.setKycStatus(KycStatus.valueOf(form.getKycStatus()));
        if (StringUtils.isNotEmpty(form.getOldUserId())) {
            user.setOldUserId(Integer.valueOf(form.getOldUserId()));
        } else {
            user.setOldUserId(null);
        }
        user.setInsideAccountFlg(form.getInsideAccountFlg());

        // -- user note
        if (org.springframework.util.StringUtils.hasText(form.getNote())) {
            UserNoteCreateForm userNoteCreateForm = new UserNoteCreateForm();
            userNoteCreateForm.setUserId(user.getId());
            userNoteCreateForm.setNote(form.getNote());
            userNoteCreateForm.setType(UserNoteTypeEnum.DEFAULT);
            userNoteCreateForm.setCurrentKycStatus(user.getKycStatus());
            userNoteCreateForm.setOperator(adminUser.getEmail());
            userNoteService.addUserNote(userNoteCreateForm);
        }

        return ResponseEntity.ok(userService.save(user));
    }

    @PutMapping("/unlock_account")
    public ResponseEntity<Object> put(
            @AuthenticationPrincipal AdminUser adminUser, @RequestParam Long userId)
            throws Exception {

        User user = userService.findOne(userId);
        if (user == null) {
            throw new CustomException(ErrorCode.AUTHENTICATION_ERROR_INVALID_USER);
        }

        user.setAccountNonLocked(true);
        return ResponseEntity.ok(userService.save(user));
    }

    @GetMapping("/affiliate-info-list")
    @PreAuthorize("@auth.check('user')")
    public ResponseEntity<List<AffiliateInfo>> get(@AuthenticationPrincipal AdminUser adminUser)
            throws Exception {
        return ResponseEntity.ok(affiliateInfoService.findAll());
    }

    @GetMapping("/user_login_info")
    @PreAuthorize("@auth.check('login-info-user')")
    public ResponseEntity<PageData<UserLoginInfo>> get(
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "ipAddress", required = false) String ipAddress,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo,
            @RequestParam(value = "number", defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(value = "size", defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws Exception {
        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        PageData<UserLoginInfo> pg =
                userLoginInfoService.findByConditionPageData(
                        userId, ipAddress, dateFrom, dateTo, number, size);
        return ResponseEntity.ok(pg);
    }

    @GetMapping("/user_login_info/download")
    public String download(
            HttpServletResponse response,
            @AuthenticationPrincipal AdminUser adminUser,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "ipAddress", required = false) String ipAddress,
            @RequestParam(value = "dateFrom", required = false) Long dateFrom,
            @RequestParam(value = "dateTo", required = false) Long dateTo)
            throws Exception {

        dateTo = dateTo != null ? dateTo + DateUnit.DAY.getMillis() : null;

        List<UserLoginInfoReportData> reportDataList = new ArrayList<UserLoginInfoReportData>();

        List<UserLoginInfo> res =
                userLoginInfoService.findByAllCondition(userId, ipAddress, dateFrom, dateTo);
        for (UserLoginInfo userLoginInfo : res) {
            reportDataList.add(new UserLoginInfoReportData().setProperties(userLoginInfo));
        }
        downloadManager.download(response, reportDataList, reportPreFix, true);
        return null;
    }
}
