package point.admin.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import point.admin.model.response.AssetData;
import point.common.component.CsvDownloadManager;
import point.common.constant.*;
import point.common.entity.Asset;
import point.common.exception.CustomException;
import point.common.model.response.OperateAssetReportData;
import point.common.model.response.PageData;
import point.common.service.AssetService;
import point.common.service.OrderbookService;

@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/admin/v1/operate/asset")
public class V1OperateAssetRestController extends ExchangeAdminController {

    private final AssetService assetService;
    private final OrderbookService orderbookService;
    private final CsvDownloadManager<OperateAssetReportData> downloadManager;

    @GetMapping
    @PreAuthorize("@auth.check('operate-assets')")
    public ResponseEntity<PageData<AssetData>> get(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "currency", required = false) String currency,
            @RequestParam(value = "onhandAmountFrom", required = false) BigDecimal onhandAmountFrom,
            @RequestParam(value = "onhandAmountTo", required = false) BigDecimal onhandAmountTo,
            @RequestParam(
                            value = "number",
                            required = false,
                            defaultValue = "" + ViewVariables.DEFAULT_NUMBER)
                    Integer number,
            @RequestParam(
                            value = "size",
                            required = false,
                            defaultValue = "" + ViewVariables.DEFAULT_SIZE)
                    Integer size)
            throws CustomException {
        // Currencyに存在するparam.currencyのみ受領する前提
        // adminは無効(enabled=false)のcurrency資産も参照可能とする
        // asset無しの場合は空リストを返す
        PageData<Asset> assetPageData =
                assetService.findByCondition(
                        userId,
                        Currency.valueOfName(currency),
                        onhandAmountFrom,
                        onhandAmountTo,
                        UserIdType.Operate,
                        number,
                        size);
        List<Asset> assetList = assetPageData.getContent();

        Map<Currency, BigDecimal> jpyConversions =
                orderbookService.getOperateBestBidOfQuoteJpy(TradeType.OPERATE);
        List<AssetData> summaryList =
                assetList.parallelStream()
                        .map(
                                asset -> {
                                    AssetData summary = new AssetData(asset);
                                    BigDecimal jpyConversion =
                                            jpyConversions.get(asset.getCurrency());
                                    summary.setOperatorPoint(
                                            asset.getOnhandAmount()
                                                    .multiply(jpyConversion)
                                                    .setScale(
                                                            CommonConstants.SCALE,
                                                            RoundingMode.HALF_UP));
                                    return summary;
                                })
                        .toList();
        PageData<AssetData> pageData =
                new PageData<>(
                        assetPageData.getNumber(),
                        assetPageData.getSize(),
                        assetPageData.getTotalElements(),
                        summaryList);
        return ResponseEntity.ok(pageData);
    }

    @GetMapping("/download")
    @PreAuthorize("@auth.check('operate-assets')")
    public String download(
            HttpServletResponse response,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "currency", required = false) String currency,
            @RequestParam(value = "onhandAmountFrom", required = false) BigDecimal onhandAmountFrom,
            @RequestParam(value = "onhandAmountTo", required = false) BigDecimal onhandAmountTo)
            throws Exception {

        List<OperateAssetReportData> reportDataList = new ArrayList<>();
        PageData<Asset> assetPageData =
                assetService.findByCondition(
                        userId,
                        Currency.valueOfName(currency),
                        onhandAmountFrom,
                        onhandAmountTo,
                        UserIdType.Operate,
                        0,
                        Integer.MAX_VALUE);
        for (Asset asset : assetPageData.getContent()) {
            reportDataList.add(new OperateAssetReportData().setProperties(asset));
        }

        String fileNamePrefix = "asset";
        downloadManager.download(
                response, reportDataList, fileNamePrefix, OperateAssetReportData.getReportHeader());
        return null;
    }
}
