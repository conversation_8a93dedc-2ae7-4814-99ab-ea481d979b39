package point.admin.model.request;

import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class PosCoverOrderUpdPutForm {

    @Getter @Setter @NotNull private Long id;

    @Getter @Setter @NotNull private BigDecimal oldRemainingAmountManual;

    @Getter @Setter @NotNull private BigDecimal remainingAmountManual;

    @Getter @Setter @NotNull private BigDecimal averagePriceManual;

    @Getter @Setter @NotNull private BigDecimal feeManual;
}
