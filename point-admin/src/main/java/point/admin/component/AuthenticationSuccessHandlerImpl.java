package point.admin.component;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.HashMap;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.WebAttributes;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.csrf.CsrfToken;
import org.springframework.stereotype.Component;
import point.admin.entity.AdminUser;
import point.admin.service.AdminUserLoginAttemptService;
import point.common.constant.Security;

@Slf4j
@RequiredArgsConstructor
@Component
public class AuthenticationSuccessHandlerImpl implements AuthenticationSuccessHandler {

    private final AdminUserLoginAttemptService adminUserLoginAttemptService;

    @Override
    public void onAuthenticationSuccess(
            HttpServletRequest request, HttpServletResponse response, Authentication authentication)
            throws IOException, ServletException {
        if (response.isCommitted()) {
            log.info("Response has already been committed.");
            return;
        }

        AdminUser adminUser =
                (AdminUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();

        HashMap<String, String> data = new HashMap<>();
        CsrfToken csrf = (CsrfToken) request.getAttribute(CsrfToken.class.getName());

        if (csrf != null) {
            data.put(Security.XSRF_TOKEN, csrf.getToken());
        }
        data.put("ROLE-ID", adminUser.getRoleId().toString());

        if (adminUser.getAuthorities() != null && adminUser.getAuthorities().size() > 0) {
            data.put("ADMIN-USER-AUTHORITY", adminUser.getAuthorities().get(0).getAuthority());
        }

        // password force change
        data.put("PASSWORD-FORCE-CHANGE", String.valueOf(adminUser.isPasswordForceChange()));

        response.getWriter().write(new ObjectMapper().writeValueAsString(data));
        clearAuthenticationAttributes(request);
        adminUserLoginAttemptService.clear(adminUser.getId());
    }

    /**
     * Removes temporary authentication-related data which may have been stored in the session
     * during the authentication process.
     */
    private void clearAuthenticationAttributes(HttpServletRequest request) {
        HttpSession session = request.getSession(false);

        if (session == null) {
            return;
        }

        session.removeAttribute(WebAttributes.AUTHENTICATION_EXCEPTION);
    }
}
