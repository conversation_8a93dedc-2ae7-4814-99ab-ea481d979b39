package point.common.model.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AccountsResponse {
    @JsonAlias("baseDate")
    private String baseDate = null;

    @JsonAlias("baseTime")
    private String baseTime = null;

    @JsonAlias("accounts")
    private List<Account> accounts = new ArrayList<>();

    private String errorCode;

    private String errorMessage;
}
