package point.common.model.request;

import lombok.*;
import point.common.model.response.VaTransactionsData;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GmoDepositDetailsForm {

    private String messageId; // メッセージを一意に識別するID

    private String timestamp; // イベントが生成された日時 ISO8601 時差(offset)も表記

    private GmoAccount account;

    private VaTransactionsData vaTransaction;
}
