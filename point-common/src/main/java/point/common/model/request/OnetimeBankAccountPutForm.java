package point.common.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

public class OnetimeBankAccountPutForm {

    @Getter @Setter @NotNull private Long id;

    @Getter @Setter private Long userId;

    @Getter @Setter @NotNull private String branchCode;

    @Getter @Setter @NotNull private String branchName;

    @Getter @Setter @NotNull private String accountNumber;
}
