package point.common.auth.common;

import point.common.config.StringConfig;

@javax.annotation.Generated(
        value = "io.swagger.codegen.languages.JavaClientCodegen",
        date = "2018-12-18T06:43:57.831Z")
public class Configuration {

    private static ApiClient personalApiClient =
            new ApiClient(StringConfig.BASE_ENDPOINT + StringConfig.PERSONAL_PATH);

    /**
     * Get the default API common.client, which would be used when creating API instances without
     * providing an API common.client.
     *
     * @return Default API common.client
     */
    public static ApiClient getPersonalApiClient() {
        return personalApiClient;
    }

    /**
     * Set the default API common.client, which would be used when creating API instances without
     * providing an API common.client.
     *
     * @param apiClient API common.client
     */
    public static void setPersonalApiClient(ApiClient apiClient) {
        personalApiClient = apiClient;
    }

    private static ApiClient authApiClient =
            new ApiClient(StringConfig.BASE_ENDPOINT + StringConfig.AUTH_PATH);

    /**
     * Get the default API common.client, which would be used when creating API instances without
     * providing an API common.client.
     *
     * @return Default API common.client
     */
    public static ApiClient getAuthApiClient() {
        return authApiClient;
    }

    /**
     * Set the default API common.client, which would be used when creating API instances without
     * providing an API common.client.
     *
     * @param apiClient API common.client
     */
    public static void setAuthApiClient(ApiClient apiClient) {
        authApiClient = apiClient;
    }

    private static ApiClient webhookApiClient =
            new ApiClient(StringConfig.BASE_ENDPOINT + StringConfig.WEBHOOK_PATH);

    /**
     * Get the default API common.client, which would be used when creating API instances without
     * providing an API common.client.
     *
     * @return Default API common.client
     */
    public static ApiClient getWebhookApiClient() {
        return webhookApiClient;
    }

    /**
     * Set the default API common.client, which would be used when creating API instances without
     * providing an API common.client.
     *
     * @param apiClient API common.client
     */
    public static void setWebhookApiClient(ApiClient apiClient) {
        webhookApiClient = apiClient;
    }
}
