package point.app.invest.model.request;

import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import point.common.model.request.IEmailForm;
import point.common.model.request.PasswordForm;

public class ResetPasswordPutForm extends PasswordForm implements IEmailForm, IMfaCodeForm {

    @Getter @Setter @NotNull private String email;

    @Getter @Setter @NotNull private String mfaCode;
}
