package point.app.component;

import com.warrenstrange.googleauth.ICredentialRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import point.common.constant.MfaType;
import point.common.entity.User;
import point.common.entity.UserMfa;
import point.common.service.UserMfaService;
import point.common.service.UserService;

@Component
@RequiredArgsConstructor
public class CredentialRepository implements ICredentialRepository {

    private final UserMfaService userMfaService;

    private final UserService userService;

    @Override
    public String getSecretKey(String userName) {
        User user = userService.findByEmail(userName);

        if (user == null) {
            return null;
        }

        UserMfa userMfa = userMfaService.findByCondition(user.getId(), MfaType.GOOGLE);
        return userMfa != null ? userMfa.getSecretKey() : null;
    }

    @Override
    public void saveUserCredentials(
            String userName, String secretKey, int validationCode, List<Integer> scratchCodes) {
        User user = userService.findByEmail(userName);

        if (user == null) {
            return;
        }

        UserMfa userMfa = userMfaService.findByCondition(user.getId(), MfaType.GOOGLE);

        if (userMfa != null) {
            if (!userMfa.isAuthenticated()) {
                userMfa.setSecretKey(secretKey);
                userMfaService.save(userMfa);
            }
        } else {
            userMfaService.save(new UserMfa(user.getId(), MfaType.GOOGLE, secretKey, false));
        }
    }
}
