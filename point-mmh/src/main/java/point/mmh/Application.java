package point.mmh;

import java.util.Date;
import java.util.Objects;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.CompositeMeterRegistryAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.aws.autoconfigure.metrics.CloudWatchExportAutoConfiguration;
import org.springframework.context.ApplicationContext;
import point.common.component.CustomLogger;
import point.common.constant.CandlestickType;
import point.common.entity.Symbol;
import point.common.service.SymbolService;
import point.mmh.component.SqsSubscriber;
import point.mmh.component.WorkerDaemon;
import point.mmh.sqssubscriber.*;
import point.pos.service.PosCandlestickService;

@SpringBootApplication(
        scanBasePackages = {"point"},
        exclude = {
            DataSourceAutoConfiguration.class,
            DataSourceTransactionManagerAutoConfiguration.class,
            HibernateJpaAutoConfiguration.class,
            CompositeMeterRegistryAutoConfiguration.class,
            CloudWatchExportAutoConfiguration.class
        })
public class Application {

    @SuppressWarnings("unchecked")
    private enum SubscriberGroup {
        ALL(
                PosBestPriceMakerSqsSubscriber.class,
                PosCoverAmberMakerSubscriber.class,
                PosCandlestickMakerSqlSubscriber.class,
                BalanceNotifyMakerSqsSubscriber.class,
                OperateAggregateOrderMakerSubscriber.class,
                OperateCoverOrderBuyMakerSqsSubscriber.class,
                OperateCoverOrderSellMakerSqsSubscriber.class,
                ChoiceActivityBasePriceUpdaterSqsSubscriber.class);

        @SuppressWarnings("rawtypes")
        private final Class<? extends SqsSubscriber>[] clazzes;

        SubscriberGroup(@SuppressWarnings({"rawtypes"}) Class<? extends SqsSubscriber>... clazzes) {
            this.clazzes = clazzes;
        }
    }

    private static final CustomLogger log = new CustomLogger(Application.class.getName());
    private static final String LOG_GROUP = "application";

    private static String getSubscriberGroupName(String[] args) {
        for (String arg : args) {
            if (arg.startsWith("-")) {
                continue;
            }

            return arg;
        }

        return null;
    }

    @SuppressWarnings("rawtypes")
    private static void subscribeQueues(
            ApplicationContext applicationContext, String subscribeGroupName) {
        log.info(LOG_GROUP, "subscribeGroupName = " + subscribeGroupName);

        SubscriberGroup subscriberGroup =
                subscribeGroupName == null
                        ? SubscriberGroup.ALL
                        : SubscriberGroup.valueOf(subscribeGroupName);

        if (Objects.isNull(subscriberGroup)) {
            log.severe(LOG_GROUP, "subscriberGroup is null");
            subscriberGroup = SubscriberGroup.ALL;
            //      return;
        }

        WorkerDaemon daemon = applicationContext.getBean(WorkerDaemon.class);

        for (Class<? extends SqsSubscriber> clazz : subscriberGroup.clazzes) {
            daemon.addSqsSubscriber(clazz);
        }
    }

    public static void main(String[] args) {
        ApplicationContext applicationContext = SpringApplication.run(Application.class, args);
        //
        //        String subscribeGroupName = getSubscriberGroupName(args);
        //
        //        subscribeQueues(applicationContext, subscribeGroupName);

        SymbolService symbolService = applicationContext.getBean(SymbolService.class);
        Symbol symbol = symbolService.findOne(202L);
        PosCandlestickService service = applicationContext.getBean(PosCandlestickService.class);
        service.make(new Symbol(), CandlestickType.PT1M, new Date());
    }
}
