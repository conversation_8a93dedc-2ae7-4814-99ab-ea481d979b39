package point.mmh.worker;

import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import point.common.constant.CandlestickType;
import point.common.entity.Symbol;
import point.mmh.component.Worker;
import point.pos.service.PosCandlestickService;

@Component
@RequiredArgsConstructor
public class PosCandlestickMaker extends Worker {

    private final PosCandlestickService posCandlestickService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) {
        Date date = new Date();

        for (CandlestickType candlestickType : CandlestickType.values()) {
            posCandlestickService.make(symbol, candlestickType, date);
        }
    }
}
