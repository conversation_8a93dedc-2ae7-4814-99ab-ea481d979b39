package point.mmh.worker;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import point.common.entity.Symbol;
import point.common.service.ChoiceActivityService;
import point.common.util.DateUnit;
import point.mmh.component.Worker;
import point.pos.model.PosBestPriceData;
import point.pos.service.PosBestPriceService;

@Slf4j
@Component
@RequiredArgsConstructor
public class ChoiceActivityBasePriceUpdater extends Worker {

    private final ChoiceActivityService choiceActivityService;

    private final PosBestPriceService posBestPriceService;

    @Override
    public void execute(Symbol symbol, Map<String, Object> params) throws Exception {
        LocalDate currentDate = DateUnit.getCurrentDateJST();
        log.info("ChoiceActivityBasePriceUpdater start dateTime: {}", currentDate);
        PosBestPriceData bestPrice = posBestPriceService.getBestPrice(202L);
        if (bestPrice == null) {
            return;
        } else {
            String voteStartTimeStr = "07:00:00";
            // 時間のフォーマッターを定義
            DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("H:mm:ss");
            LocalTime voteStartTime = LocalTime.parse(voteStartTimeStr, timeFormatter);
            LocalDateTime startDateTimeJST = LocalDateTime.of(currentDate, voteStartTime);
            ZoneId jstZone = ZoneId.of("Asia/Tokyo");
            ZonedDateTime startZonedJST = startDateTimeJST.atZone(jstZone);
            ZonedDateTime startZonedUTC = startZonedJST.withZoneSameInstant(ZoneOffset.UTC);
            log.info("ChoiceActivityBasePriceUpdater startZonedUTC: {}", startZonedUTC);
            choiceActivityService.updateBasePriceVotingDay(
                    Date.from(startZonedUTC.toInstant()), bestPrice.getBestAsk());
            choiceActivityService.updateBasePriceElectionDay(
                    Date.from(startZonedUTC.minusDays(1).toInstant()), bestPrice.getBestAsk());
        }
        LocalDateTime endDateTime = LocalDateTime.now();
        log.info("ChoiceActivityBasePriceUpdater end dateTime: {}", endDateTime);
    }
}
