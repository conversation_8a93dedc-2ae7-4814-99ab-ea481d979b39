package point.worker;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.ApplicationContext;
import point.common.component.CustomLogger;
import point.common.entity.Symbol;
import point.mmh.worker.PosCandlestickMaker;
import point.worker.component.SqsSubscriber;
import point.worker.component.WorkerDaemon;
import point.worker.sqssubscriber.*;

@SpringBootApplication(
        scanBasePackages = {"point"},
        exclude = {
            DataSourceAutoConfiguration.class,
            DataSourceTransactionManagerAutoConfiguration.class,
            HibernateJpaAutoConfiguration.class
        })
public class Application {

    @SuppressWarnings("unchecked")
    private enum SubscriberGroup {
        ALL(
                AssetSummaryCalculatorSqsSubscriber.class,
                OperateAssetSummaryCalculatorSqsSubscriber.class,
                AssetSummaryRecalculatorSqsSubscriber.class,
                ExchangeSummarySqsSubscriber.class,
                OperateExchangeSummarySqsSubscriber.class,
                FinancialAssetsDeviationCheckerSqsSubscriber.class,
                HighValueTraderCheckerSqsSubscriber.class,
                InvestmentPurposeDeviationCheckerSqsSubscriber.class,
                SameIpCheckerSqsSubscriber.class,
                PosOrderArchiverSqsSubscriber.class,
                PosTradeArchiverSqsSubscriber.class,
                UserSummaryCalculatorSqsSubscriber.class,
                GmoDepositCheckerSqsSubscriber.class,
                GmoTokenUpdaterSqsSubscriber.class,
                GmoDepositReconcileSqsSubscriber.class,
                IssueVACheckerSqsSubscriber.class,
                BulkTransferExecutorSqsSubscriber.class,
                BulkTransferResultCheckerSqsSubscriber.class,
                UserEkycBpoResultUpdaterSqsSubscriber.class,
                UserAntiSocialCheckerSqsSubscriber.class,
                YearlyReportMakerSqsSubscriber.class,
                PointPartnerStatusUpdaterSqsSubscriber.class,
                CircuitBreakerSqsSubscriber.class,
                PointUserSummaryCalculatorSqsSubscriber.class,
                ChoicePowerCalculatorSqsSubscriber.class,
                ChoicePowerMonthlyCalculatorSqsSubscriber.class,
                ChoiceActivityMakerSqsSubscriber.class,
                ChoiceActivityStatusMakerSqsSubscriber.class,
                QuizQuestionPublishCalculatorSqsSubscriber.class,
                UserVoteRewardCalculatorSqsSubscriber.class,
                PontaGrantPointUploadSqsSubscriber.class,
                PontaGrantPointHulftParseSqsSubscriber.class,
                ChoiceActivityTemplateStatusUpdaterSqsSubscriber.class,
                UserVoteRewardExpireSqsSubscriber.class,
                PontaConverMakerSqsSubscriber.class);

        @SuppressWarnings("rawtypes")
        private final Class<? extends SqsSubscriber>[] clazzes;

        SubscriberGroup(@SuppressWarnings({"rawtypes"}) Class<? extends SqsSubscriber>... clazzes) {
            this.clazzes = clazzes;
        }
    }

    private static final CustomLogger log = new CustomLogger(Application.class.getName());
    private static final String LOG_GROUP = "application";

    private static String getSubscriberGroupName(String[] args) {
        for (String arg : args) {
            if (arg.startsWith("-")) {
                continue;
            }

            return arg;
        }

        return null;
    }

    @SuppressWarnings("rawtypes")
    private static void subscribeQueues(
            ApplicationContext applicationContext, String subscribeGroupName) {
        log.info(LOG_GROUP, "subscribeGroupName = " + subscribeGroupName);

        SubscriberGroup subscriberGroup =
                subscribeGroupName == null
                        ? SubscriberGroup.ALL
                        : SubscriberGroup.valueOf(subscribeGroupName);

        if (subscriberGroup == null) {
            log.severe(LOG_GROUP, "subscriberGroup is null");
            subscriberGroup = SubscriberGroup.ALL;
            //      return;
        }

        WorkerDaemon daemon = applicationContext.getBean(WorkerDaemon.class);

        for (Class<? extends SqsSubscriber> clazz : subscriberGroup.clazzes) {
            daemon.addSqsSubscriber(clazz);
        }
    }

    public static void main(String[] args) throws Exception {
        ApplicationContext applicationContext = SpringApplication.run(Application.class, args);

        //        String subscribeGroupName = getSubscriberGroupName(args);
        //
        //        subscribeQueues(applicationContext, subscribeGroupName);
        //        PontaGrantPointHulftUpload hulftUpload =
        //                applicationContext.getBean(PontaGrantPointHulftUpload.class);
        //        hulftUpload.execute(new Symbol(), null);

        PosCandlestickMaker posCandlestickMaker =
                applicationContext.getBean(PosCandlestickMaker.class);
        posCandlestickMaker.execute(new Symbol(), null);
    }
}
