import os

def rename_files_and_dirs(root_dir):
    for dirpath, dirnames, filenames in os.walk(root_dir, topdown=False):
        # Rename files
        for filename in filenames:
            if 'exchange' in filename:
                new_filename = filename.replace('exchange', 'point')
                os.rename(os.path.join(dirpath, filename), os.path.join(dirpath, new_filename))
        
        # Rename directories
        for dirname in dirnames:
            if 'exchange' in dirname:
                new_dirname = dirname.replace('exchange', 'point')
                os.rename(os.path.join(dirpath, dirname), os.path.join(dirpath, new_dirname))

# Set the root directory
root_directory = 'bs-point-server'
rename_files_and_dirs(root_directory)